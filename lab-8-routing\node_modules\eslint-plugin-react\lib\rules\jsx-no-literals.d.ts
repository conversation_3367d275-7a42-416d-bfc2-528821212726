declare const _exports: RuleModule;
export = _exports;
export type RuleModule = import("eslint").Rule.RuleModule;
export type Config = {
    type: "element";
} & import("../../types/rules/jsx-no-literals").ElementConfigProperties & import("../../types/rules/jsx-no-literals").ElementOverrides;
export type RawConfig = import("../../types/rules/jsx-no-literals").RawElementConfig & import("../../types/rules/jsx-no-literals").RawElementOverrides;
export type ResolvedConfig = import("../../types/rules/jsx-no-literals").OverrideConfig | import("../../types/rules/jsx-no-literals").Config;
export type OverrideConfig = import("../../types/rules/jsx-no-literals").OverrideConfigProperties & import("../../types/rules/jsx-no-literals").ElementConfigProperties;
export type ElementConfig = {
    type: "element";
} & import("../../types/rules/jsx-no-literals").ElementConfigProperties;
//# sourceMappingURL=jsx-no-literals.d.ts.map